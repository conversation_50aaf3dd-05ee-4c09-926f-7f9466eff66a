import {
  type BasePost,
  type ChallengePostGroups,
  type ChallengePostTeams,
  timestampToDate,
} from '@types';

export const areChallengePostsEqual = (
  a: ChallengePostTeams | ChallengePostGroups,
  b: ChallengePostTeams | ChallengePostGroups,
): boolean =>
  a.content === b.content &&
  a.authorUserId === b.authorUserId &&
  a.challengeId === b.challengeId &&
  (a as ChallengePostTeams).teamId === (b as ChallengePostTeams).teamId &&
  (a as ChallengePostGroups).groupId === (b as ChallengePostGroups).groupId &&
  a.imageUrl === b.imageUrl &&
  a.linkUrl === b.linkUrl;

export const sortPostsDescending = (a: BasePost, b: BasePost) =>
  timestampToDate(b.createdDateTime).getTime() - timestampToDate(a.createdDateTime).getTime();
