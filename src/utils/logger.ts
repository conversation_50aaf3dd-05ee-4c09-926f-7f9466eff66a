// import {getAnalytics, logEvent} from 'firebase/analytics';
import log from 'loglevel';
import {DEV_FEATURE_FLAGS} from '@constants';

const LOG_LEVEL_LOCAL = 'debug';
// const LOG_LEVEL_ENDPOINT = 'DEBUG';
// const LOG_PREFIX = '' as string;
// const LOG_USE_ENDPOINT = true as boolean;

log.setLevel(LOG_LEVEL_LOCAL);

/**
 * To hook into the log() method, we have to derive our own method factory.
 * Pulled from loglevel documentation: https://github.com/pimterry/loglevel#writing-plugins
 */
// const originalFactory = log.methodFactory;
// log.methodFactory = function (methodName, logLevel, loggerName) {
//   const rawMethod = originalFactory(methodName, logLevel, loggerName);

//   return function (...messages: unknown[]) {
//     // Call the base logging method
//     rawMethod(...messages);
//   };
// };
log.setLevel(log.getLevel()); // Be sure to call setLevel method in order to apply plugin

export {default as LOGGER} from 'loglevel';

// const LOG_LEVELS = log.levels;

// type LogLevelsType = log.LogLevel[keyof log.LogLevel];

const createLocalLoggerSubLabel =
  (
    labelTitle: (subLabel: string) => string,
    featureFlag: keyof ReturnType<typeof DEV_FEATURE_FLAGS>,
    level: 'debug' | 'warn' | 'error' = 'debug',
  ) =>
    (subLabel: unknown, ...args: unknown[]) =>
      DEV_FEATURE_FLAGS()[featureFlag] && log[level](labelTitle(JSON.stringify(subLabel)), ...args);

const createLocalLogger =
  (
    labelTitle: string,
    featureFlag: keyof ReturnType<typeof DEV_FEATURE_FLAGS>,
    level: 'debug' | 'warn' | 'error' = 'debug',
  ) =>
    (...args: unknown[]) => {
      DEV_FEATURE_FLAGS()[featureFlag] && log[level](labelTitle, ...args);
    };

export const envLog = createLocalLogger('[Env]', 'isDebugEnv');

export const healthSyncLog = createLocalLoggerSubLabel(
  label => `[Health ${label}]`,
  'isDebugHealthSync',
);
export const healthSyncWarn = createLocalLoggerSubLabel(
  label => `[Health ${label}]`,
  'isDebugHealthSync',
  'warn',
);
export const notificationLog = createLocalLogger('[Notification]', 'isDebugNotification');
export const notificationError = createLocalLogger(
  '[Notification]',
  'isDebugNotification',
  'error',
);
export const navigationLog = createLocalLogger('[Navigation]', 'isDebugNavigation');
export const navigationError = createLocalLogger('[Navigation]', 'isDebugNavigation', 'error');
export const authLogger = createLocalLogger('[Auth]', 'isDebugAuth');
export const authLoggerError = createLocalLogger('[Auth]', 'isDebugAuth', 'error');
export const strictError = createLocalLogger('[StrictMode]', 'isDebugStrictMode', 'error');
export const imageLogger = createLocalLogger('[Image]', 'isDebugImage');
export const imageLoggerError = createLocalLogger('[Image]', 'isDebugImage', 'error');
export const mmkvLogger = createLocalLogger('[MMKV]', 'isDebugMMKVEnabled');
export const fitbitLog = createLocalLogger('[Fitbit]', 'isDebugFitbit');
export const fitbitLogError = createLocalLogger('[Fitbit]', 'isDebugFitbit', 'error');
export const trackingDeviceLog = createLocalLogger('[TrackingDevice]', 'isDebugTrackingDevice');
export const backgroundStateLog = createLocalLoggerSubLabel(
  label => `[AppState] [${label}]`,
  'isDebugAppStateBackground',
);
export const challengeLog = createLocalLogger('[Challenge]', 'isDebugChallenge');
export const challengeLogError = createLocalLogger('[Challenge]', 'isDebugChallenge', 'error');
export const calendarLog = createLocalLogger('[Calendar]', 'isDebugCalendar');
export const moveStreakLog = createLocalLogger('[MovementStreak]', 'isDebugMovementStreak');
export const queryLog = createLocalLoggerSubLabel(label => `[Query] [${label}]`, 'isDebugQueries');
export const debugCacheLog = createLocalLogger('[Query Cache]', 'isDebugCache');
export const analyticsConsoleLog = createLocalLogger('[Analytics]', 'isConsoleAnalyticsEnabled');
export const actionSheetLog = createLocalLogger('[ActionSheet]', 'isActionSheetEnabled');
