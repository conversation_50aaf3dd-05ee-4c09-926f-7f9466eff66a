import {
  createNavigationContainerRef,
  type NavigationProp,
  type NavigatorScreenParams,
  StackActions,
  useRoute,
} from '@react-navigation/native';
import {useNavigation} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {endOfMonth, startOfMonth} from 'date-fns';
import {useCallback, useEffect, useMemo} from 'react';
import {createMaterialBottomTabNavigator} from 'react-native-paper/react-navigation';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {useCurrentTimeZoneWithDefault, useDateEveryDayChanged} from '@hooks';
import {
  type CalendarParams,
  type CalendarParamsDefined,
  CalendarViewType,
  type ChallengeGroupingType,
  type DateInterval,
  getIsoMonthStringFromDate,
  type Initializer,
  isCalendarViewType,
  type IsoDate,
  type IsoDateFull,
  type IsoMonth,
  isValidDateString,
  isValidIsoMonth,
  type Meal,
  type OptionalIfUndefined,
  type TimeZones,
  type UUIDString,
  type Workout,
} from '@types';
import {getDateFromIsoDate, getIsoStringFromDate} from '../dates-timing-conversion';
import {getUpdatedParams, type UpdateParamOptions} from '../domain';
import {LOGGER} from '../logger';
import {getInitializer, isDeepEqual} from '../primitives';

export {NavigationContainer, useNavigation, useRoute} from '@react-navigation/native';

type ShareParams = {
  shareWorkoutId?: UUIDString | undefined;
};

type AuthParams = {
  email?: string;
  lang?: string;
  oobCode?: string;
  token?: string;
};

export type WeightScreenParams = {
  isoMonth: IsoMonth | undefined;
};

export type HomeStackParamList = {
  'create-meal-screen': {
    initialMeal?: Partial<Meal>;
    initialStartDate?: IsoDateFull;
    isTrainerCreate?: true | undefined;
    userSearchTerm: string;
  };
  'create-summary-report': {
    challengeSearchTerm?: string;
    organizationSearchTerm?: string;
    userSearchTerm?: string;
  };
  'create-workout-screen': {
    initialStartDate?: IsoDateFull;
    initialWorkout?: Partial<Workout>;
    isTrainerCreate?: true | undefined;
    userSearchTerm: string;
  };
  'edit-manual-entry': {date: IsoDate; userId: UUIDString};
  'edit-meal-screen': {
    mealId: UUIDString;
    userSearchTerm: string;
  };
  'edit-profile': {userId: UUIDString};
  'edit-workout-screen': {userSearchTerm: string; workoutId: UUIDString};
  'health-sync-settings-screen'?: undefined;
  'home-screen'?: Partial<CalendarParams> & ShareParams;
  'login-loading-screen': undefined;
  'login-screen'?: AuthParams;
  'notification-settings-screen'?: undefined;
  'privacy-policy-confirmation': undefined;
  'profile': {userId: UUIDString};
  'settings-screen': undefined;
  'sign-up-screen': undefined;
  'summary-reports': undefined;
  'weight-screen'?: WeightScreenParams;
};

export type ChallengesStackParamList = {
  'about-challenges-screen'?: undefined;
  'challenge-admin-screen': {challengeId: UUIDString};
  'challenges-screen'?: undefined;
  'create-challenge-screen': {
    groupingType: ChallengeGroupingType;
    userSearchTerm: string;
  };
  'edit-challenge-post-screen': {
    challengeId: UUIDString;
    challengePostId: UUIDString;
    groupId?: UUIDString | null | undefined;
  };
  'edit-challenge-screen': {
    challengeId: UUIDString;
    teamId?: UUIDString | undefined;
    userSearchTerm: string;
  };
  'trainer-message-screen': {
    challengeId: UUIDString;
    groupId?: UUIDString | undefined | null;
    teamId?: UUIDString | undefined | null;
  };
  'view-challenge-screen': {challengeId: UUIDString};
};

export type CoachStackParamList = {
  'coach-home-screen': {clientSearchTerm: string};
  'create-user-screen': {organizationSearch: string};
  'edit-manual-entry': {date: IsoDate; userId: UUIDString};
  'edit-meal-screen': {mealId: UUIDString; userSearchTerm: string};
  'edit-user-screen': {organizationSearch: string; userId: UUIDString};
  'edit-workout-screen': {userSearchTerm: string; workoutId: UUIDString};
  'view-client-screen': {clientId: UUIDString} & Partial<CalendarParams>;
};

export type AdminStackParamList = {
  'admin-home-screen'?: {organizationsSearchTerm?: string; usersSearchTerm?: string};
  'create-organization-screen': {
    adminUsersSearch: string;
    clientUsersSearch: string;
    coachUsersSearch: string;
    parentOrganizationSearch: string;
  };
  'create-user-screen': {organizationSearch: string};
  'edit-user-screen': {organizationSearch: string; userId: UUIDString};
  'view-organization-screen': {
    adminUsersSearch: string;
    clientUsersSearch: string;
    coachUsersSearch: string;
    organizationId: UUIDString;
    parentOrganizationSearch: string;
  };
};

export type FeedStackParamList = {
  'create-post-screen': {organizationId: UUIDString};
  'edit-post-screen': {organizationId: UUIDString; postId: UUIDString};
  'organization-feed'?: {organizationId?: UUIDString | undefined};
};

// Routes for the bottom tab navigator
export type BottomStackParamList = {
  'admin-page': NavigatorScreenParams<AdminStackParamList>;
  'challenges-page': NavigatorScreenParams<ChallengesStackParamList>;
  'coach-page': NavigatorScreenParams<CoachStackParamList>;
  'feed-page': NavigatorScreenParams<FeedStackParamList>;
  'home-page': NavigatorScreenParams<HomeStackParamList>;
};

// eslint-disable-next-line @typescript-eslint/no-unsafe-assignment -- TODO: fix
export const BottomStack = createMaterialBottomTabNavigator<BottomStackParamList>();
export type HomePageProps = NativeStackScreenProps<BottomStackParamList, 'home-page'>;
export type ChallengesPageProps = NativeStackScreenProps<BottomStackParamList, 'challenges-page'>;
export type CoachHomePageProps = NativeStackScreenProps<BottomStackParamList, 'coach-page'>;
export type AdminPageProps = NativeStackScreenProps<BottomStackParamList, 'admin-page'>;
export type FeedPageProps = NativeStackScreenProps<BottomStackParamList, 'feed-page'>;
type BottomStackPages = keyof BottomStackParamList;

export const HomeStack = createNativeStackNavigator<HomeStackParamList>();

export const ChallengesStack = createNativeStackNavigator<ChallengesStackParamList>();
export type ChallengesScreenProps = NativeStackScreenProps<
  ChallengesStackParamList,
  'challenges-screen'
>;
export type ChallengesCreateChallengeScreenProps = NativeStackScreenProps<
  ChallengesStackParamList,
  'create-challenge-screen'
>;
export type EditChallengeScreenProps = NativeStackScreenProps<
  ChallengesStackParamList,
  'edit-challenge-screen'
>;
export type ViewChallengeScreenProps = NativeStackScreenProps<
  ChallengesStackParamList,
  'view-challenge-screen'
>;
export type ChallengeAdminScreenProps = NativeStackScreenProps<
  ChallengesStackParamList,
  'challenge-admin-screen'
>;
export type ChallengesAboutScreenProps = NativeStackScreenProps<
  ChallengesStackParamList,
  'about-challenges-screen'
>;
export type ChallengeEditPostScreenProps = NativeStackScreenProps<
  ChallengesStackParamList,
  'edit-challenge-post-screen'
>;
export type ChallengeTrainerMessageScreenProps = NativeStackScreenProps<
  ChallengesStackParamList,
  'trainer-message-screen'
>;

export const CoachStack = createNativeStackNavigator<CoachStackParamList>();
export type CoachHomeScreenProps = NativeStackScreenProps<CoachStackParamList, 'coach-home-screen'>;
export type CoachViewClientScreenProps = NativeStackScreenProps<
  CoachStackParamList,
  'view-client-screen'
>;
export type CoachCreateUserScreenProps = NativeStackScreenProps<
  CoachStackParamList,
  'create-user-screen'
>;
export type CoachEditUserScreenProps = NativeStackScreenProps<
  CoachStackParamList,
  'edit-user-screen'
>;

export const FeedStack = createNativeStackNavigator<FeedStackParamList>();
export type FeedCreatePostScreenProps = NativeStackScreenProps<
  FeedStackParamList,
  'create-post-screen'
>;
export type FeedEditPostScreenProps = NativeStackScreenProps<
  FeedStackParamList,
  'edit-post-screen'
>;
export type FeedScreenProps = NativeStackScreenProps<FeedStackParamList, 'organization-feed'>;

export const AdminStack = createNativeStackNavigator<AdminStackParamList>();
export type AdminHomeScreenProps = NativeStackScreenProps<AdminStackParamList, 'admin-home-screen'>;
export type AdminViewAndEditOrganizationScreenProps = NativeStackScreenProps<
  AdminStackParamList,
  'view-organization-screen'
>;
export type AdminCreateOrganizationScreenProps = NativeStackScreenProps<
  AdminStackParamList,
  'create-organization-screen'
>;
export type AdminCreateUserScreenProps = NativeStackScreenProps<
  AdminStackParamList,
  'create-user-screen'
>;
export type AdminEditUserScreenProps = NativeStackScreenProps<
  AdminStackParamList,
  'edit-user-screen'
>;

type AllStackParamList = HomeStackParamList &
  ChallengesStackParamList &
  CoachStackParamList &
  AdminStackParamList &
  FeedStackParamList;

export type AllRouteNames = keyof AllStackParamList;

export const navigationRef = createNavigationContainerRef();

const getCurrentParams = () => navigationRef.getCurrentRoute()?.params ?? {};

const useSetNavigationParams = <
  T extends AllRouteNames,
  P extends Partial<AllStackParamList[T]>,
>() => {
  const {setParams} = useNavigation();

  return useCallback(
    (value: Initializer<Partial<P> | undefined>) => {
      if (!navigationRef.isReady()) {
        LOGGER.warn('Navigation is not ready');
        return;
      }
      const prevParams = getCurrentParams();
      const newParams = getInitializer(value, prevParams);
      const hasChanged = !isDeepEqual(prevParams, newParams);
      if (!hasChanged) return;
      // @ts-ignore - untyped setParams
      setParams(newParams);
    },
    [setParams],
  );
};

export const useLinkTo = () => {
  const {dispatch, goBack, navigate} = useNavigation<NavigationProp<AllStackParamList>>();

  const createLink = useMemo(
    () =>
      <T extends AllRouteNames>(
        route: T,
        parentRoute?: BottomStackPages | BottomStackPages[],
      ) =>
        (
          params:
            | OptionalIfUndefined<AllStackParamList[T]>
            | ((
              prevParams?: OptionalIfUndefined<AllStackParamList[T]>,
            ) => OptionalIfUndefined<AllStackParamList[T]>),
        ) => {
          if (!navigationRef.isReady()) {
            LOGGER.warn('Navigation is not ready');
            return;
          }
          const prevParams = getCurrentParams() as OptionalIfUndefined<AllStackParamList[T]>;
          // Always shallow merge params
          const mergedParams =
          typeof params === 'function' ? params(prevParams) : {...prevParams, ...params};

          // Determine parent route dynamically if an array is provided
          let effectiveParentRoute = parentRoute;
          if (Array.isArray(parentRoute)) {
          // Get current route state
            const state = navigationRef.getRootState();

            // Use the index to determine which route is active
            const activeRouteIndex = state?.index ?? 0;
            const activeRoute = state?.routes?.[activeRouteIndex]?.name as
              | BottomStackPages
              | undefined;

            // Check if the active route is in our allowed parent routes
            const isActiveRouteAllowed = activeRoute && parentRoute.includes(activeRoute);

            // Use the active route if it's in our allowed list, otherwise default to first
            effectiveParentRoute = isActiveRouteAllowed ? activeRoute : parentRoute[0];
          }

          // @ts-expect-error to allow for the generic type to be inferred
          navigate<T>(effectiveParentRoute ?? route, {
            params: mergedParams,
            ...(effectiveParentRoute ? {screen: route} : {}),
            // eslint-disable-next-line @typescript-eslint/naming-convention -- library API
            initial: false, // to ensure the first screen of the stack is treated as the first to allow back functionality
          });
        },
    [navigate],
  );

  return useMemo(
    () => ({
      goBack,
      pop: () => dispatch(StackActions.pop()),
      popToTop: () => dispatch(StackActions.popToTop()),
      popTo: <TT extends AllRouteNames>(routeName: TT, params?: AllStackParamList[TT]) =>
        dispatch(StackActions.popTo(routeName, params)),
      loginScreen: createLink('login-screen', 'home-page'),
      signUpScreen: createLink('sign-up-screen', 'home-page'),
      createWorkoutScreen: createLink('create-workout-screen', 'home-page'),
      createMealScreen: createLink('create-meal-screen', 'home-page'),
      profile: createLink('profile', 'home-page'),
      editProfile: createLink('edit-profile', 'home-page'),
      homeScreen: createLink('home-screen', 'home-page'),
      settingsScreen: createLink('settings-screen', 'home-page'),
      notificationSettingsScreen: createLink('notification-settings-screen', 'home-page'),
      weightScreen: createLink('weight-screen', 'home-page'),
      summaryReports: createLink('summary-reports', 'home-page'),
      createSummaryReport: createLink('create-summary-report', 'home-page'),
      healthSyncSettings: createLink('health-sync-settings-screen', 'home-page'),
      challengesScreen: createLink('challenges-screen', 'challenges-page'),
      createChallengeScreen: createLink('create-challenge-screen', 'challenges-page'),
      editChallengeScreen: createLink('edit-challenge-screen', 'challenges-page'),
      viewChallengeScreen: createLink('view-challenge-screen', 'challenges-page'),
      aboutChallengesScreen: createLink('about-challenges-screen', 'challenges-page'),
      editChallengePost: createLink('edit-challenge-post-screen', 'challenges-page'),
      trainerMessageScreen: createLink('trainer-message-screen', 'challenges-page'),
      challengeAdminScreen: createLink('challenge-admin-screen', 'challenges-page'),
      coachHomeScreen: createLink('coach-home-screen', 'coach-page'),
      createUserCoach: createLink('create-user-screen', 'coach-page'),
      viewClientScreen: createLink('view-client-screen', 'coach-page'),
      adminHomeScreen: createLink('admin-home-screen', 'admin-page'),
      viewOrganizationScreen: createLink('view-organization-screen', 'admin-page'),
      createOrganizationScreen: createLink('create-organization-screen', 'admin-page'),
      createNewUser: createLink('create-user-screen', 'admin-page'),
      organizationFeed: createLink('organization-feed', 'feed-page'),
      createOrganizationPost: createLink('create-post-screen', 'feed-page'),
      editOrganizationPost: createLink('edit-post-screen', 'feed-page'),
      // Shared routes, exists on multiple pages
      editWorkoutScreen: createLink('edit-workout-screen', ['home-page', 'coach-page']),
      editMealScreen: createLink('edit-meal-screen', ['home-page', 'coach-page']),
      editManualEntryHome: createLink('edit-manual-entry', ['home-page', 'coach-page']),
      editUserScreen: createLink('edit-user-screen', ['admin-page', 'coach-page']),
    }),
    [dispatch, goBack, createLink],
  );
};
export const BOTTOM_TABS_HEIGHT = 80;

const createUseParams =
  <R extends AllRouteNames, ExpectedType = Readonly<AllStackParamList[R]>>(
    validator?: (params: unknown) => params is ExpectedType,
  ) =>
    () => {
      // Use useRoute hook which is more reliable than useNavigationState for getting current route params
      const route = useRoute();
      const params = route?.params as ExpectedType;

      // Optionally validate the params
      if (validator && params !== undefined && !validator(params)) {
        LOGGER.warn(`Invalid params: ${JSON.stringify(params)}`);
      }

      return params;
    };

export const useProfileParams = createUseParams<'profile'>();
export const useEditProfileParams = createUseParams<'edit-profile'>();
export const useEditWorkoutParams = createUseParams<'edit-workout-screen'>();
export const useCreateWorkoutParams = createUseParams<'create-workout-screen'>();
export const useEditManualEntryParams = createUseParams<'edit-manual-entry'>();
export const useEditMealParams = createUseParams<'edit-meal-screen'>();
export const useCreateMealParams = createUseParams<'create-meal-screen'>();
export const useChallengesCreateChallengeParams = createUseParams<'create-challenge-screen'>();

export const useCalendarParams = createUseParams<
  'home-screen',
  CalendarParamsDefined | undefined
>();

const getInitialInterval = (currentDate: Date, timeZone: TimeZones) => ({
  start: getIsoStringFromDate(startOfMonth(currentDate), timeZone),
  end: getIsoStringFromDate(endOfMonth(currentDate), timeZone),
});

const getDefaultCalendarParams = (
  currentDate: Date,
  timeZone: TimeZones,
): CalendarParamsDefined => ({
  calendarSelectedDate: getIsoStringFromDate(currentDate, timeZone),
  calendarViewType: CalendarViewType.DAY,
  ...getInitialInterval(currentDate, timeZone),
});

const isValidCalendarParamsDefined = (params: unknown): params is CalendarParamsDefined =>
  !!params &&
  typeof params === 'object' &&
  'calendarSelectedDate' in params &&
  isValidDateString(params.calendarSelectedDate) &&
  'calendarViewType' in params &&
  isCalendarViewType(params.calendarViewType) &&
  'start' in params &&
  isValidDateString(params.start) &&
  'end' in params &&
  isValidDateString(params.end);

const isValidHomeScreenParams = (params: unknown): params is CalendarParams =>
  isValidCalendarParamsDefined(params);

export const useDefaultCalendarParamsEffect = () => {
  const currentDate = useDateEveryDayChanged();
  const setParams = useSetNavigationParams<'home-screen', CalendarParams>();
  const currentParams = useCalendarParams();
  const timeZone = useCurrentTimeZoneWithDefault();
  useEffect(() => {
    const isValidParams = isValidHomeScreenParams(currentParams);

    // Only call setParams if params are invalid or need updates
    if (!isValidParams) {
      setParams(prevParams => {
        const defaultParams = getDefaultCalendarParams(currentDate, timeZone);
        const mergedParams = {...prevParams, ...defaultParams};

        // Check if merging changes the params
        if (!isDeepEqual(mergedParams, prevParams)) return mergedParams;

        return prevParams; // No update necessary
      });
    }
  }, [currentDate, setParams, currentParams, timeZone]);
};

export const useClearShareParams = () => {
  const setParams = useSetNavigationParams<'home-screen', ShareParams>();
  return useCallback(() => {
    setParams(prevParams => {
      if (!prevParams?.shareWorkoutId) return prevParams;
      const {shareWorkoutId: _, ...newParams} = prevParams;
      return newParams;
    });
  }, [setParams]);
};

export const useCalendarIntervalIsoDate = () => {
  const currentDate = useDateEveryDayChanged();
  const params = useCalendarParams();
  const timeZone = useCurrentTimeZoneWithDefault();
  const initialInterval = useMemo(
    () => getInitialInterval(currentDate, timeZone),
    [currentDate, timeZone],
  );
  if (!params?.start || !params.end) return initialInterval;
  return params;
};

const isValidWeightScreenParams = (params: unknown): params is WeightScreenParams =>
  // @ts-ignore -- inferred
  params && typeof params === 'object' && isValidIsoMonth(params?.isoMonth);

const getDefaultWeightParams = (currentDate: Date, timeZone: TimeZones): WeightScreenParams => ({
  isoMonth: getIsoMonthStringFromDate(currentDate, timeZone),
});

export const useWeightScreenParams = createUseParams<
  'weight-screen',
  WeightScreenParams | undefined
>();

export const useDefaultWeightScreenParamsEffect = () => {
  const currentDate = useDateEveryDayChanged();
  const timeZone = useCurrentTimeZoneWithDefault();
  const setParams = useSetNavigationParams<'weight-screen', WeightScreenParams>();
  const currentParams = useWeightScreenParams();
  useEffect(() => {
    setParams(prevParams => {
      const isValidParams = isValidWeightScreenParams(prevParams);
      if (isValidParams) return prevParams;
      return {...prevParams, ...getDefaultWeightParams(currentDate, timeZone)};
    });
    // eslint-disable-next-line react-hooks-addons/no-unused-deps -- trigger when currentParams changes (currently possible via navigating directly to screen from sub-screen)
  }, [currentDate, setParams, currentParams, timeZone]);
};

export const useCalendarIntervalDate = (): DateInterval => {
  const {end, start} = useCalendarIntervalIsoDate();
  const timeZone = useCurrentTimeZoneWithDefault();
  return useMemo(
    () => ({
      startDate: getDateFromIsoDate(start, timeZone),
      endDate: getDateFromIsoDate(end, timeZone),
    }),
    [start, timeZone, end],
  );
};

export const useManageCalendarParams = () => {
  const setParams = useSetNavigationParams<'home-screen', CalendarParamsDefined>();

  return useCallback(
    (options: UpdateParamOptions) => {
      setParams(prevParams => {
        const isValidParams = isValidHomeScreenParams(prevParams);
        if (!prevParams || !isValidParams) return prevParams;

        return getUpdatedParams(prevParams, options);
      });
    },
    [setParams],
  );
};

export const useResetSelectedDay = () => {
  const manageCalendarParams = useManageCalendarParams();
  const currentDate = useDateEveryDayChanged();
  const timeZone = useCurrentTimeZoneWithDefault();
  return useCallback(() => {
    const calendarSelectedDate = getIsoStringFromDate(currentDate, timeZone);
    manageCalendarParams({calendarSelectedDate});
  }, [currentDate, manageCalendarParams, timeZone]);
};
