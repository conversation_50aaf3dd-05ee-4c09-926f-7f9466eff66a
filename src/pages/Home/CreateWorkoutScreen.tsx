import {useCallback} from 'react';
import {EditWorkout, ScreenContent, ScreenHeader} from '@components';
import {CONTENT_CODES} from '@constants';
import {ScreenWrapper, useAddSnack, useAppUserSafe} from '@contexts';
import {useInitialState} from '@hooks';
import {useCreateWorkoutParams, useLinkTo} from '@navigation';
import {emptyCreateWorkout} from '@utils';

export const CreateWorkoutScreen: React.FC = () => {
  const {initialStartDate, initialWorkout, isTrainerCreate = false} = useCreateWorkoutParams();
  const {id} = useAppUserSafe();
  const to = useLinkTo();
  const addSnack = useAddSnack();

  const initialWorkoutValue = useInitialState(
    emptyCreateWorkout(
      initialWorkout ?? {
        trainerIds: [id],
        participantIds: isTrainerCreate ? [] : [id],
      },
      initialStartDate ? new Date(initialStartDate) : new Date(),
    ),
  );

  const onSubmitSuccess = useCallback(() => {
    addSnack('Workout created successfully ✅');
    to.pop();
  }, [addSnack, to]);

  return (
    <ScreenWrapper isModalScreen>
      <ScreenHeader
        title={
          isTrainerCreate
            ? CONTENT_CODES().WORKOUT.CREATE_WORKOUT_TRAINER_HEADER
            : CONTENT_CODES().WORKOUT.CREATE_WORKOUT_SELF_HEADER
        }
      />

      <ScreenContent>
        <EditWorkout
          isFirstCreate
          hasAddParticipants={isTrainerCreate}
          searchQueryKey=''
          submitText={CONTENT_CODES().WORKOUT.SUBMIT_BUTTON}
          value={initialWorkoutValue}
          onSubmitSuccess={onSubmitSuccess}
        />
      </ScreenContent>
    </ScreenWrapper>
  );
};
