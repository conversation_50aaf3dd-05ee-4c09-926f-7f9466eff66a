import {useMutation} from '@tanstack/react-query';
import {useCallback, useState} from 'react';
import {DOMAIN_CONSTANTS} from '@constants';
import {useImageChange} from '@data-hooks';
import {
  type BaseAppUser,
  type Challenge,
  type ImageUrl,
  type Initializer,
  isTrainerMessagePostGroups,
  isTrainerMessagePostTeams,
  patternMatch,
  type UUIDString,
} from '@types';
import {
  emptyTrainerMessagePostGroups,
  emptyTrainerMessagePostTeams,
  getInitializer,
  isDeepEqual,
  isEmptyArray,
  LOGGER,
  uuid,
} from '@utils';
import {useChallengePostSubmit} from './createChallengePostContext';
import {useAddSnack} from './snacks';

// eslint-disable-next-line max-lines-per-function -- handling 2 two types of posts
export const useCreateChallengeTrainerMessage = (
  challenge: Challenge,
  trainerAppUser: BaseAppUser,
  onSuccess: () => void,
  options: {
    allGroups: {id: UUIDString; name: string}[] | undefined;
    initialSelectedGroupId?: UUIDString | undefined | null;
    initialSelectedTeamId?: UUIDString | undefined | null;
  },
) => {
  const type =
    options.initialSelectedTeamId === undefined ? ('groups' as const) : ('teams' as const);
  // Track the initial state in order to reset the form when it changes
  const [initialStateImmutable] = useState(
    patternMatch(type)
      .with('teams', () =>
        emptyTrainerMessagePostTeams(
          challenge.id,
          trainerAppUser.id,
          options.initialSelectedTeamId,
        ),
      )
      .with('groups', () =>
        emptyTrainerMessagePostGroups(
          challenge.id,
          trainerAppUser.id,
          options.initialSelectedGroupId,
        ),
      )
      .exhaustive(),
  );
  const [trainerMessagePost, setTrainerMessagePost] = useState(initialStateImmutable);
  const {mutateAsync} = useChallengePostSubmit(challenge.id);

  const onReset = useCallback(() => {
    setTrainerMessagePost(initialStateImmutable);
  }, [initialStateImmutable]);

  const addSnack = useAddSnack();
  const onSubmitFn = useCallback(async () => {
    const posts = patternMatch(trainerMessagePost)
      .when(isTrainerMessagePostTeams, value => {
        const {teamIds, ...postWithoutTeams} = value;
        return teamIds.map(teamId => ({
          ...postWithoutTeams,
          id: uuid(),
          teamId,
        }));
      })
      .when(isTrainerMessagePostGroups, value => {
        const {groupIds, ...postWithoutGroups} = value;
        return groupIds.map(groupId => ({
          ...postWithoutGroups,
          id: uuid(),
          groupId,
        }));
      })
      .exhaustive();

    try {
      await mutateAsync(posts);
      onSuccess();
    } catch {
      addSnack('There was an issue submitting the trainer message');
    }
  }, [addSnack, mutateAsync, onSuccess, trainerMessagePost]);
  const onSubmitMutation = useMutation({
    mutationFn: onSubmitFn,
  });

  const onContentChange = useCallback((content: string) => {
    // Extract first HTTPS URL from content
    const httpsUrlRegex = /https:\/\/[^\s]+/g;
    const urls = content.match(httpsUrlRegex);
    const firstUrl = urls?.[0];

    LOGGER.debug('[Trainer Message] Content changed:', {content, detectedUrl: firstUrl});

    setTrainerMessagePost(prev => {
      const updatedPost = {
        ...prev,
        content,
      };

      if (firstUrl) {
        updatedPost.linkUrl = firstUrl;
        LOGGER.debug('[Trainer Message] Link URL set:', firstUrl);
      } else {
        delete updatedPost.linkUrl;
        LOGGER.debug('[Trainer Message] Link URL cleared');
      }

      return updatedPost;
    });
  }, []);

  const onIdsChange = useCallback((ids: Initializer<(UUIDString | null)[]>) => {
    setTrainerMessagePost(prev =>
      patternMatch(prev)
        .when(isTrainerMessagePostGroups, value => ({
          ...value,
          groupIds: getInitializer(ids, value.groupIds),
        }))
        .when(isTrainerMessagePostTeams, value => ({
          ...value,
          teamIds: getInitializer(ids, value.teamIds),
        }))
        .exhaustive(),
    );
  }, []);

  const removeImage = useCallback(() => {
    setTrainerMessagePost(prev => {
      const {imageUrl: _, ...rest} = prev;
      return rest;
    });
  }, []);

  const applyImage = useCallback((imageUrl: ImageUrl) => {
    setTrainerMessagePost(prev => ({
      ...prev,
      imageUrl,
    }));
  }, []);

  const {isImagePending, onImageChange, onImagePaste} = useImageChange({
    imageUploadPathPrefix: patternMatch(trainerMessagePost)
      .when(isTrainerMessagePostTeams, value =>
        DOMAIN_CONSTANTS().CLOUD_STORAGE_PATH_PREFIXES.CHALLENGE_TEAM_PHOTO(
          value.challengeId,
          null,
        ),
      )
      .when(isTrainerMessagePostGroups, value =>
        DOMAIN_CONSTANTS().CLOUD_STORAGE_PATH_PREFIXES.CHALLENGE_GROUP_PHOTO(
          value.challengeId,
          null,
        ),
      )
      .exhaustive(),

    removeImage,
    applyImage,
  });

  const hasChanged = !isDeepEqual(trainerMessagePost, initialStateImmutable);
  const isNoGroupSelected = patternMatch(trainerMessagePost)
    .when(isTrainerMessagePostTeams, value => isEmptyArray(value.teamIds))
    .when(isTrainerMessagePostGroups, value => isEmptyArray(value.groupIds))
    .exhaustive();
  const isAllGroupsSelected = patternMatch(trainerMessagePost)
    .when(isTrainerMessagePostTeams, value => value.teamIds.length === options.allGroups?.length)
    .when(isTrainerMessagePostGroups, value => value.groupIds.length === options.allGroups?.length)
    .exhaustive();
  const isValid = trainerMessagePost.content.length > 0 && !isNoGroupSelected && hasChanged;
  const selectedGroup = patternMatch(trainerMessagePost)
    .when(isTrainerMessagePostTeams, value =>
      options.allGroups?.find(g => g.id === value.teamIds[0]),
    )
    .when(isTrainerMessagePostGroups, value =>
      options.allGroups?.find(g => g.id === value.groupIds[0]),
    )
    .exhaustive();

  return {
    trainerMessagePost,
    onContentChange,
    onImageChange,
    onImagePaste,
    isImagePending,
    isValid,
    isPendingSubmit: onSubmitMutation.isPending,
    onSubmit: onSubmitMutation.mutateAsync,
    onReset,
    onIdsChange,
    hasChanged,
    isAllGroupsSelected,
    isNoGroupSelected,
    selectedGroup,
    type,
  };
};
