import {useCallback, useEffect, useRef, useState} from 'react';
import {DOMAIN_CONSTANTS} from '@constants';
import {useImageChange} from '@data-hooks';
import {
  dateToRawTimestamp,
  getNowAsRawTimestampClient,
  type ImageUrl,
  type OrganizationPost,
  type UUIDString,
} from '@types';
import {areOrganizationPostsEqual, LOGGER} from '@utils';
import {useBasePost} from './basePostContext';
import {useOrganizationPostMutation} from './firestore';

const useOrganizationPostSubmit = (orgId: UUIDString, onSuccess: () => void) => {
  const {isPending: isPendingSubmit, mutateAsync} = useOrganizationPostMutation(orgId);
  const submit = useCallback(
    async (org: OrganizationPost) => {
      const post: OrganizationPost = {
        ...org,
        lastModifiedDateTime: dateToRawTimestamp(new Date()),
      };
      await mutateAsync(post);
      LOGGER.debug('[Organization Post] Organization post submitted successfully', post.id);
      onSuccess();
    },
    [mutateAsync, onSuccess],
  );
  return {submit, isPendingSubmit};
};

export const useEditOrganizationPost = (
  isFirstCreate: boolean,
  orgId: UUIDString,
  initialState: OrganizationPost,
  onSuccess: () => void,
) => {
  const basePost = useBasePost({
    initialState,
    isPostEqual: areOrganizationPostsEqual,
    imageUploadPathPrefix: DOMAIN_CONSTANTS().CLOUD_STORAGE_PATH_PREFIXES.ORGANIZATION_POST(
      orgId,
      initialState.id,
    ),
    logPrefix: 'Organization Post',
  });

  const {
    isChanged,
    isImagePending,
    onContentChange,
    onImageChange,
    onImagePaste,
    onReset,
    post,
    setInitialStateImmutable,
  } = basePost;

  const {isPendingSubmit, submit} = useOrganizationPostSubmit(orgId, onSuccess);
  const onSubmit = useCallback(async () => {
    const postWithUpdatedDateTime = isFirstCreate
      ? {
          ...post,
          createdDateTime: getNowAsRawTimestampClient(), // Update created time to time it was submitted at
        }
      : post;
    await submit(postWithUpdatedDateTime);
    setInitialStateImmutable(initialState);
  }, [initialState, isFirstCreate, post, submit, setInitialStateImmutable]);

  return {
    isPendingSubmit,
    post,
    onSubmit,
    onContentChange,
    isImagePending,
    onImageChange,
    onImagePaste,
    onReset,
    isChanged,
  };
};
