import {useCallback, useEffect, useRef, useState} from 'react';
import {DOMAIN_CONSTANTS} from '@constants';
import {useImageChange} from '@data-hooks';
import {
  dateToRawTimestamp,
  getNowAsRawTimestampClient,
  type ImageUrl,
  type OrganizationPost,
  type UUIDString,
} from '@types';
import {areOrganizationPostsEqual, LOGGER} from '@utils';
import {useOrganizationPostMutation} from './firestore';

const useOrganizationPostSubmit = (orgId: UUIDString, onSuccess: () => void) => {
  const {isPending: isPendingSubmit, mutateAsync} = useOrganizationPostMutation(orgId);
  const submit = useCallback(
    async (org: OrganizationPost) => {
      const post: OrganizationPost = {
        ...org,
        lastModifiedDateTime: dateToRawTimestamp(new Date()),
      };
      await mutateAsync(post);
      LOGGER.debug('[Organization Post] Organization post submitted successfully', post.id);
      onSuccess();
    },
    [mutateAsync, onSuccess],
  );
  return {submit, isPendingSubmit};
};

export const useEditOrganizationPost = (
  isFirstCreate: boolean,
  orgId: UUIDString,
  initialState: OrganizationPost,
  onSuccess: () => void,
) => {
  // Track the initial state in order to reset the form when it changes
  const [initialStateImmutable, setInitialStateImmutable] = useState(initialState);
  const [post, setPost] = useState(initialStateImmutable);

  // Reset the initial state when the initial state initializer changes
  const isSkipFirstRender = useRef(true);
  useEffect(() => {
    if (isSkipFirstRender.current) {
      isSkipFirstRender.current = false;
      return;
    }
    setInitialStateImmutable(initialState);
    setPost(initialState);
  }, [initialState]);

  const {isPendingSubmit, submit} = useOrganizationPostSubmit(orgId, onSuccess);
  const onSubmit = useCallback(async () => {
    const postWithUpdatedDateTime = isFirstCreate
      ? {
          ...post,
          createdDateTime: getNowAsRawTimestampClient(), // Update created time to time it was submitted at
        }
      : post;
    await submit(postWithUpdatedDateTime);
    setInitialStateImmutable(initialState);
  }, [initialState, isFirstCreate, post, submit]);

  const onContentChange = useCallback((content: string) => {
    // Extract first HTTPS URL from content
    const httpsUrlRegex = /https:\/\/[^\s]+/g;
    const urls = content.match(httpsUrlRegex);
    const firstUrl = urls?.[0];

    LOGGER.debug('[Organization Post] Content changed:', {content, detectedUrl: firstUrl});

    setPost(prev => {
      const updatedPost = {
        ...prev,
        content,
      };

      if (firstUrl) {
        updatedPost.linkUrl = firstUrl;
        LOGGER.debug('[Organization Post] Link URL set:', firstUrl);
      } else {
        delete updatedPost.linkUrl;
        LOGGER.debug('[Organization Post] Link URL cleared');
      }

      return updatedPost;
    });
  }, []);

  const removeImage = useCallback(() => {
    setPost(prev => {
      const {imageUrl: _, ...rest} = prev;
      return rest;
    });
  }, []);

  const applyImage = useCallback((imageUrl: ImageUrl) => {
    setPost(prev => ({...prev, imageUrl}));
  }, []);

  const {isImagePending, onImageChange, onImagePaste} = useImageChange({
    imageUploadPathPrefix: DOMAIN_CONSTANTS().CLOUD_STORAGE_PATH_PREFIXES.ORGANIZATION_POST(
      orgId,
      post.id,
    ),
    removeImage,
    applyImage,
  });

  const onReset = useCallback(() => {
    setPost(initialStateImmutable);
  }, [initialStateImmutable]);

  const isChanged = !areOrganizationPostsEqual(initialStateImmutable, post);

  return {
    isPendingSubmit,
    post,
    onSubmit,
    onContentChange,
    isImagePending,
    onImageChange,
    onImagePaste,
    onReset,
    isChanged,
  };
};
