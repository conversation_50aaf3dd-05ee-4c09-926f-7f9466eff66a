import {useMutation} from '@tanstack/react-query';
import {useCallback} from 'react';
import {DOMAIN_CONSTANTS} from '@constants';
import {
  type ChallengePostBase,
  type ChallengePostGroups,
  type ChallengePostTeams,
  dateToRawTimestamp,
  getNowAsRawTimestampClient,
  isChallengePostGroups,
  type UUIDString,
} from '@types';
import {areChallengePostsEqual, ensureArray, getAllFulfilledOrThrow, LOGGER} from '@utils';
import {useBasePost} from './basePostContext';
import {useChallengePostMutation} from './firestore';

// Helper to prepare posts for submission
const preparePostsForSubmit = <TPost extends ChallengePostBase>(
  partialPost: TPost | TPost[],
): TPost[] =>
  ensureArray(partialPost).map(p => ({
    ...p,
    lastModifiedDateTime: dateToRawTimestamp(new Date()),
  }));

export const useChallengePostSubmit = <T extends ChallengePostTeams | ChallengePostGroups>(
  challengeId: UUIDString,
  onSuccess?: (posts: T[]) => void,
) => {
  const baseMutation = useChallengePostMutation<T>(challengeId);
  const mutationFn = useCallback(
    async (partialPost: T | T[]) => {
      const allPosts = preparePostsForSubmit(partialPost);
      const promises = allPosts.map(p =>
        baseMutation.mutateAsync({
          data: p,
          params: isChallengePostGroups(p) ? {groupId: p.groupId} : undefined,
        }),
      );
      await getAllFulfilledOrThrow(promises);
      LOGGER.debug(
        '[Challenge Group Post] Challenge group posts submitted successfully',
        allPosts.map(p => p.id),
      );
      onSuccess?.(allPosts);
    },
    [baseMutation, onSuccess],
  );

  return useMutation({
    mutationFn,
  });
};

export const useEditChallengePostTeams = (
  isFirstCreate: boolean,
  initialState: ChallengePostTeams,
  onSuccess: () => void,
) => {
  const {
    isChanged,
    isImagePending,
    onContentChange,
    onImageChange,
    onImagePaste,
    onReset,
    post,
    setInitialStateImmutable,
  } = useBasePost({
    initialState,
    isPostEqual: areChallengePostsEqual,
    imageUploadPathPrefix: DOMAIN_CONSTANTS().CLOUD_STORAGE_PATH_PREFIXES.CHALLENGE(
      initialState.challengeId,
      initialState.id,
    ),
    logPrefix: 'Challenge Post',
  });

  const submitMutation = useChallengePostSubmit(post.challengeId, onSuccess);
  const onSubmit = useCallback(async () => {
    const postWithUpdatedDateTime = isFirstCreate
      ? {
          ...post,
          createdDateTime: getNowAsRawTimestampClient(), // Update created time to time it was submitted at
        }
      : post;
    await submitMutation.mutateAsync(postWithUpdatedDateTime);
    setInitialStateImmutable(initialState);
  }, [initialState, isFirstCreate, post, submitMutation, setInitialStateImmutable]);

  return {
    isPendingSubmit: submitMutation.isPending,
    post,
    onSubmit,
    onContentChange,
    isImagePending,
    onImageChange,
    onImagePaste,
    onReset,
    isChanged,
  };
};

export const useEditChallengePostGroups = (
  isFirstCreate: boolean,
  initialState: ChallengePostGroups,
  onSuccess: () => void,
) => {
  const basePost = useBasePost({
    initialState,
    isPostEqual: areChallengePostsEqual,
    imageUploadPathPrefix: DOMAIN_CONSTANTS().CLOUD_STORAGE_PATH_PREFIXES.CHALLENGE_GROUP_PHOTO(
      initialState.challengeId,
      initialState.groupId,
    ),
    logPrefix: 'Challenge Post Groups',
  });

  const {
    isChanged,
    isImagePending,
    onContentChange,
    onImageChange,
    onImagePaste,
    onReset,
    post,
    setInitialStateImmutable,
  } = basePost;

  const {isPending: isPendingSubmit, mutateAsync: submit} = useChallengePostSubmit(
    post.challengeId,
    onSuccess,
  );
  const onSubmit = useCallback(async () => {
    const postWithUpdatedDateTime = isFirstCreate
      ? {
          ...post,
          createdDateTime: getNowAsRawTimestampClient(), // Update created time to time it was submitted at
        }
      : post;
    await submit(postWithUpdatedDateTime);
    setInitialStateImmutable(initialState);
  }, [initialState, isFirstCreate, post, submit, setInitialStateImmutable]);

  return {
    isPendingSubmit,
    post,
    onSubmit,
    onContentChange,
    isImagePending,
    onImageChange,
    onImagePaste,
    onReset,
    isChanged,
  };
};
