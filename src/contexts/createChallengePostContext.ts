import {useMutation} from '@tanstack/react-query';
import {useCallback, useEffect, useRef, useState} from 'react';
import {DOMAIN_CONSTANTS} from '@constants';
import {useImageChange} from '@data-hooks';
import {
  type ChallengePostBase,
  type ChallengePostGroups,
  type ChallengePostTeams,
  dateToRawTimestamp,
  getNowAsRawTimestampClient,
  type ImageUrl,
  isChallengePostGroups,
  type UUIDString,
} from '@types';
import {areChallengePostsEqual, ensureArray, getAllFulfilledOrThrow, LOGGER} from '@utils';
import {useChallengePostMutation} from './firestore';

// Helper to prepare posts for submission
const preparePostsForSubmit = <TPost extends ChallengePostBase>(
  partialPost: TPost | TPost[],
): TPost[] =>
  ensureArray(partialPost).map(p => ({
    ...p,
    lastModifiedDateTime: dateToRawTimestamp(new Date()),
  }));

export const useChallengePostSubmit = <T extends ChallengePostTeams | ChallengePostGroups>(
  challengeId: UUIDString,
  onSuccess?: (posts: T[]) => void,
) => {
  const baseMutation = useChallengePostMutation<T>(challengeId);
  const mutationFn = useCallback(
    async (partialPost: T | T[]) => {
      const allPosts = preparePostsForSubmit(partialPost);
      const promises = allPosts.map(p =>
        baseMutation.mutateAsync({
          data: p,
          params: isChallengePostGroups(p) ? {groupId: p.groupId} : undefined,
        }),
      );
      await getAllFulfilledOrThrow(promises);
      LOGGER.debug(
        '[Challenge Group Post] Challenge group posts submitted successfully',
        allPosts.map(p => p.id),
      );
      onSuccess?.(allPosts);
    },
    [baseMutation, onSuccess],
  );

  return useMutation({
    mutationFn,
  });
};

export const useEditChallengePostTeams = (
  isFirstCreate: boolean,
  initialState: ChallengePostTeams,
  onSuccess: () => void,
) => {
  // Track the initial state in order to reset the form when it changes
  const [initialStateImmutable, setInitialStateImmutable] = useState(initialState);
  const [post, setPost] = useState(initialStateImmutable);

  // Reset/ the initial state when the initial state initializer changes
  const isSkipFirstRender = useRef(true);
  useEffect(() => {
    if (isSkipFirstRender.current) {
      isSkipFirstRender.current = false;
      return;
    }
    setInitialStateImmutable(initialState);
  }, [initialState]);
  // Reset/clear the form when the initial state changes
  useEffect(() => {
    setPost(p => (p === initialStateImmutable ? p : initialStateImmutable));
  }, [initialStateImmutable]);

  const submitMutation = useChallengePostSubmit(post.challengeId, onSuccess);
  const onSubmit = useCallback(async () => {
    const postWithUpdatedDateTime = isFirstCreate
      ? {
          ...post,
          createdDateTime: getNowAsRawTimestampClient(), // Update created time to time it was submitted at
        }
      : post;
    await submitMutation.mutateAsync(postWithUpdatedDateTime);
    setInitialStateImmutable(initialState);
  }, [initialState, isFirstCreate, post, submitMutation]);

  const onContentChange = useCallback((content: string) => {
    // Extract first HTTPS URL from content
    const httpsUrlRegex = /https:\/\/[^\s]+/g;
    const urls = content.match(httpsUrlRegex);
    const firstUrl = urls?.[0];

    LOGGER.debug('[Challenge Post] Content changed:', {content, detectedUrl: firstUrl});

    setPost(prev => {
      const updatedPost = {
        ...prev,
        content,
      };

      if (firstUrl) {
        updatedPost.linkUrl = firstUrl;
        LOGGER.debug('[Challenge Post] Link URL set:', firstUrl);
      } else {
        delete updatedPost.linkUrl;
        LOGGER.debug('[Challenge Post] Link URL cleared');
      }

      return updatedPost;
    });
  }, []);

  const removeImage = useCallback(() => {
    setPost(prev => {
      const {imageUrl: _, ...rest} = prev;
      return rest;
    });
  }, []);

  const applyImage = useCallback((imageUrl: ImageUrl) => {
    setPost(prev => ({...prev, imageUrl}));
  }, []);

  const {isImagePending, onImageChange, onImagePaste} = useImageChange({
    imageUploadPathPrefix: DOMAIN_CONSTANTS().CLOUD_STORAGE_PATH_PREFIXES.CHALLENGE(
      post.challengeId,
      post.id,
    ),
    removeImage,
    applyImage,
  });

  const onReset = useCallback(() => {
    setPost(initialStateImmutable);
  }, [initialStateImmutable]);

  const isChanged = !areChallengePostsEqual(initialStateImmutable, post);

  return {
    isPendingSubmit: submitMutation.isPending,
    post,
    onSubmit,
    onContentChange,
    isImagePending,
    onImageChange,
    onImagePaste,
    onReset,
    isChanged,
  };
};

export const useEditChallengePostGroups = (
  isFirstCreate: boolean,
  initialState: ChallengePostGroups,
  onSuccess: () => void,
) => {
  // Track the initial state in order to reset the form when it changes
  const [initialStateImmutable, setInitialStateImmutable] = useState(initialState);
  const [post, setPost] = useState(initialStateImmutable);

  // Reset/ the initial state when the initial state initializer changes
  const isSkipFirstRender = useRef(true);
  useEffect(() => {
    if (isSkipFirstRender.current) {
      isSkipFirstRender.current = false;
      return;
    }
    setInitialStateImmutable(initialState);
    setPost(initialState);
  }, [initialState]);

  const {isPending: isPendingSubmit, mutateAsync: submit} = useChallengePostSubmit(
    post.challengeId,
    onSuccess,
  );
  const onSubmit = useCallback(async () => {
    const postWithUpdatedDateTime = isFirstCreate
      ? {
          ...post,
          createdDateTime: getNowAsRawTimestampClient(), // Update created time to time it was submitted at
        }
      : post;
    await submit(postWithUpdatedDateTime);
    setInitialStateImmutable(initialState);
  }, [initialState, isFirstCreate, post, submit]);

  const onContentChange = useCallback((content: string) => {
    // Extract first HTTPS URL from content
    const httpsUrlRegex = /https:\/\/[^\s]+/g;
    const urls = content.match(httpsUrlRegex);
    const firstUrl = urls?.[0];

    LOGGER.debug('[Challenge Post Groups] Content changed:', {content, detectedUrl: firstUrl});

    setPost(prev => {
      const updatedPost = {
        ...prev,
        content,
      };

      if (firstUrl) {
        updatedPost.linkUrl = firstUrl;
        LOGGER.debug('[Challenge Post Groups] Link URL set:', firstUrl);
      } else {
        delete updatedPost.linkUrl;
        LOGGER.debug('[Challenge Post Groups] Link URL cleared');
      }

      return updatedPost;
    });
  }, []);

  const removeImage = useCallback(() => {
    setPost(prev => {
      const {imageUrl: _, ...rest} = prev;
      return rest;
    });
  }, []);

  const applyImage = useCallback((imageUrl: ImageUrl) => {
    setPost(prev => ({...prev, imageUrl}));
  }, []);

  const {isImagePending, onImageChange, onImagePaste} = useImageChange({
    imageUploadPathPrefix: DOMAIN_CONSTANTS().CLOUD_STORAGE_PATH_PREFIXES.CHALLENGE_GROUP_PHOTO(
      post.challengeId,
      post.groupId,
    ),
    removeImage,
    applyImage,
  });

  const onReset = useCallback(() => {
    setPost(initialStateImmutable);
  }, [initialStateImmutable]);

  const isChanged = !areChallengePostsEqual(initialStateImmutable, post);

  return {
    isPendingSubmit,
    post,
    onSubmit,
    onContentChange,
    isImagePending,
    onImageChange,
    onImagePaste,
    onReset,
    isChanged,
  };
};
