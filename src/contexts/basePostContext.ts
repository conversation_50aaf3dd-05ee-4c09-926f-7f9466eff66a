import {useCallback, useEffect, useRef, useState} from 'react';
import {useImageChange} from '@data-hooks';
import {type BasePost, type ImageUrl} from '@types';
import {LOGGER} from '@utils';

export type BasePostHookConfig<TPost extends BasePost> = {
  imageUploadPathPrefix: `${string}/`;
  initialState: TPost;
  isPostEqual: (a: TPost, b: TPost) => boolean;
  logPrefix: string;
};

/**
 * Shared hook for post content management with URL detection, image handling, and state management.
 * Provides common functionality for all post types that extend BasePost.
 */
export const useBasePost = <TPost extends BasePost>(config: BasePostHookConfig<TPost>) => {
  const {
    imageUploadPathPrefix,
    initialState,
    isPostEqual,
    logPrefix,
  } = config;

  // Track the initial state in order to reset the form when it changes
  const [initialStateImmutable, setInitialStateImmutable] = useState(initialState);
  const [post, setPost] = useState<TPost>(initialStateImmutable);

  // Reset the initial state when the initial state initializer changes
  const isSkipFirstRender = useRef(true);
  useEffect(() => {
    if (isSkipFirstRender.current) {
      isSkipFirstRender.current = false;
      return;
    }
    setInitialStateImmutable(initialState);
    setPost(initialState);
  }, [initialState]);

  /**
   * Content change handler with automatic HTTPS URL detection and linkUrl setting
   */
  const onContentChange = useCallback((content: string) => {
    // Extract first HTTPS URL from content
    const httpsUrlRegex = /https:\/\/[^\s]+/g;
    const urls = content.match(httpsUrlRegex);
    const firstUrl = urls?.[0];

    LOGGER.debug(`[${logPrefix}] Content changed:`, {content, detectedUrl: firstUrl});

    setPost(prev => {
      const updatedPost = {
        ...prev,
        content,
      } as TPost;

      if (firstUrl) {
        updatedPost.linkUrl = firstUrl;
        LOGGER.debug(`[${logPrefix}] Link URL set:`, firstUrl);
      } else {
        delete updatedPost.linkUrl;
        LOGGER.debug(`[${logPrefix}] Link URL cleared`);
      }

      return updatedPost;
    });
  }, [logPrefix]);

  /**
   * Remove image from the post
   */
  const removeImage = useCallback(() => {
    setPost(prev => {
      const {imageUrl: _, ...rest} = prev;
      return rest as TPost;
    });
  }, []);

  /**
   * Apply/set image on the post
   */
  const applyImage = useCallback((imageUrl: ImageUrl) => {
    setPost(prev => ({...prev, imageUrl} as TPost));
  }, []);

  /**
   * Image handling hook integration
   */
  const {isImagePending, onImageChange, onImagePaste} = useImageChange({
    imageUploadPathPrefix,
    removeImage,
    applyImage,
  });

  /**
   * Reset post to initial state
   */
  const onReset = useCallback(() => {
    setPost(initialStateImmutable);
  }, [initialStateImmutable]);

  /**
   * Check if the post has changed from initial state
   */
  const isChanged = !isPostEqual(initialStateImmutable, post);

  return {
    // State
    post,
    setPost,
    initialStateImmutable,
    setInitialStateImmutable,
    isChanged,

    // Content handling
    onContentChange,

    // Image handling
    isImagePending,
    onImageChange,
    onImagePaste,
    removeImage,
    applyImage,

    // Reset functionality
    onReset,
  };
};

/**
 * Utility function to create a content change handler with URL detection
 * For contexts that need custom state management but want URL detection functionality
 */
export const createContentChangeWithUrlDetection = <TPost extends BasePost>(
  setPost: React.Dispatch<React.SetStateAction<TPost>>,
  logPrefix: string,
) => (content: string) => {
  // Extract first HTTPS URL from content
  const httpsUrlRegex = /https:\/\/[^\s]+/g;
  const urls = content.match(httpsUrlRegex);
  const firstUrl = urls?.[0];

  LOGGER.debug(`[${logPrefix}] Content changed:`, {content, detectedUrl: firstUrl});

  setPost(prev => {
    const updatedPost = {
      ...prev,
      content,
    } as TPost;

    if (firstUrl) {
      updatedPost.linkUrl = firstUrl;
      LOGGER.debug(`[${logPrefix}] Link URL set:`, firstUrl);
    } else {
      delete updatedPost.linkUrl;
      LOGGER.debug(`[${logPrefix}] Link URL cleared`);
    }

    return updatedPost;
  });
};
